# Google Analytics 4 & Google Ads Tracking Implementation

This document describes the comprehensive tracking implementation for the Astro-based multilingual website, including Google Analytics 4 (GA4), Google Ads tracking, and GDPR-compliant cookie consent.

## 🚀 Features Implemented

### Google Analytics 4 (GA4)
- ✅ Complete GA4 setup with multilingual support
- ✅ Enhanced ecommerce tracking for rest area listings
- ✅ Custom events for location searches, language switching, and navigation
- ✅ Core Web Vitals monitoring
- ✅ Scroll depth tracking
- ✅ Form submission tracking
- ✅ Page type and language dimension tracking

### Google Ads Integration
- ✅ Conversion tracking for key user actions
- ✅ Remarketing tags for visitor retargeting
- ✅ Enhanced conversions for better accuracy
- ✅ Privacy-compliant restricted data processing

### Privacy & GDPR Compliance
- ✅ Cookie consent banner with granular controls
- ✅ Multilingual consent interface (English/Polish)
- ✅ Consent state management with localStorage
- ✅ Automatic consent expiration (1 year)
- ✅ Opt-out mechanisms for all tracking

### Technical Features
- ✅ Environment variable configuration
- ✅ Development/production mode handling
- ✅ Error handling and fallbacks
- ✅ Performance optimization
- ✅ Astro component architecture

## 📁 File Structure

```
src/
├── components/
│   ├── GoogleAnalytics.astro      # GA4 tracking component
│   ├── GoogleAds.astro            # Google Ads tracking component
│   ├── CookieConsent.astro        # GDPR cookie consent banner
│   ├── TrackingManager.astro      # Centralized tracking manager
│   ├── LanguageSelector.astro     # Updated with tracking
│   └── RestAreaCard.astro         # Updated with tracking attributes
├── layouts/
│   └── Layout.astro               # Updated with tracking integration
├── pages/
│   └── index.astro                # Updated with search tracking
├── i18n/
│   ├── en.json                    # English translations (updated)
│   └── pl.json                    # Polish translations (updated)
└── ...

.env.example                       # Environment variables template
astro.config.mjs                   # Updated with environment config
scripts/test-tracking.js           # Testing script
README-tracking.md                 # This documentation
```

## ⚙️ Setup Instructions

### 1. Environment Configuration

Copy the environment template:
```bash
cp .env.example .env
```

Configure your tracking IDs in `.env`:
```env
# Google Analytics 4 Configuration
PUBLIC_GA4_MEASUREMENT_ID=G-XXXXXXXXXX

# Google Ads Configuration
PUBLIC_GOOGLE_ADS_ID=123-456-7890
PUBLIC_GOOGLE_ADS_CONVERSION_ID=AW-123456789/AbC-D_efG-h_i-jk

# Privacy Configuration
PUBLIC_ENABLE_COOKIE_CONSENT=true
PUBLIC_ENABLE_ANALYTICS=true
PUBLIC_ENABLE_DEV_ANALYTICS=false
```

### 2. Google Analytics 4 Setup

1. Create a GA4 property in Google Analytics
2. Get your Measurement ID (format: G-XXXXXXXXXX)
3. Configure custom dimensions in GA4:
   - `dimension1`: page_type
   - `dimension2`: language
   - `dimension3`: location_hierarchy

### 3. Google Ads Setup

1. Get your Google Ads Customer ID
2. Create conversion actions in Google Ads
3. Get your Conversion ID (format: AW-123456789/AbC-D_efG-h_i-jk)

### 4. Testing

Run the test script to verify implementation:
```bash
node scripts/test-tracking.js
```

## 📊 Tracking Events

### Automatic Events
- **Page Views**: Enhanced with page type and language
- **Scroll Depth**: 25%, 50%, 75%, 90%, 100% milestones
- **Core Web Vitals**: CLS, FID, FCP, LCP, TTFB, INP
- **Link Clicks**: Internal and external link tracking
- **Form Submissions**: All form interactions

### Custom Events
- **Location Search**: `trackLocationSearch(searchData)`
- **Rest Area View**: `trackRestAreaView(restAreaData)`
- **Language Switch**: `trackLanguageSwitch(fromLang, toLang)`
- **Dark Mode Toggle**: `trackDarkModeToggle(mode)`
- **High Engagement**: Time on page + scroll depth

### Conversion Events
- **Search Conversion**: Location searches with results
- **View Conversion**: Rest area detail views
- **Engagement Conversion**: High-value user interactions
- **Form Conversion**: Contact form submissions

## 🔒 Privacy Compliance

### Cookie Consent
The implementation includes a GDPR-compliant cookie consent banner with:
- **Essential Cookies**: Always enabled (required for functionality)
- **Analytics Cookies**: Optional (Google Analytics)
- **Advertising Cookies**: Optional (Google Ads, remarketing)

### Consent Management
- Consent choices are stored in localStorage
- Consent expires after 1 year
- Users can revoke consent at any time
- Tracking respects user preferences

### Data Processing
- Anonymized IP addresses
- Restricted data processing for ads (until consent)
- No personal data collection without consent
- GDPR-compliant data handling

## 🌍 Multilingual Support

The tracking system fully supports the multilingual setup:
- Language-specific event tracking
- Localized cookie consent interface
- URL structure preservation
- Language switching tracking

## 🛠️ Development

### Enable Development Tracking
Set in `.env`:
```env
PUBLIC_ENABLE_DEV_ANALYTICS=true
```

### Debug Mode
The implementation includes console logging for debugging:
- Event tracking calls
- Consent state changes
- Error handling

### Testing Checklist
- [ ] GA4 events appear in Real-Time reports
- [ ] Google Ads conversions are recorded
- [ ] Cookie consent banner appears
- [ ] Language switching works
- [ ] Search tracking functions
- [ ] Rest area view tracking works

## 📈 Analytics Configuration

### GA4 Custom Dimensions
Configure these in your GA4 property:
1. **page_type**: Custom dimension for page categorization
2. **language**: Custom dimension for language tracking
3. **location_hierarchy**: Custom dimension for location-based analysis

### Google Ads Conversion Actions
Set up these conversion actions:
1. **Location Search**: When users search for locations
2. **Rest Area View**: When users view rest area details
3. **High Engagement**: When users show high engagement
4. **Form Submission**: When users submit forms

## 🔧 Customization

### Adding New Events
Use the global tracking manager:
```javascript
window.trackingManager.trackCustomEvent({
  type: 'custom_action',
  value: 'action_value',
  additional_data: 'extra_info'
});
```

### Custom Conversions
Add new conversion tracking:
```javascript
window.trackCustomConversion({
  type: 'custom_conversion',
  value: 10.0,
  currency: 'EUR'
});
```

## 🚨 Important Notes

1. **Production Only**: Tracking only loads in production unless explicitly enabled
2. **Consent Required**: Ads tracking requires user consent
3. **Performance**: All tracking is optimized for minimal performance impact
4. **Privacy First**: Implementation follows privacy-by-design principles
5. **GDPR Compliant**: Full compliance with European privacy regulations

## 📞 Support

For issues or questions about the tracking implementation:
1. Check the browser console for error messages
2. Verify environment variables are set correctly
3. Test in production mode or with dev analytics enabled
4. Review Google Analytics Real-Time reports
5. Check Google Ads conversion tracking

The implementation is now ready for your Google Analytics 4 Measurement ID and Google Ads Conversion ID!
