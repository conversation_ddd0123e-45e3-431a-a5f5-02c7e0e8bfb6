#!/usr/bin/env node

/**
 * Test script for tracking implementation
 * This script helps verify that tracking components are properly integrated
 */

import { readFileSync } from 'fs';
import { join } from 'path';

const projectRoot = process.cwd();

function checkFile(filePath, description) {
  try {
    const content = readFileSync(join(projectRoot, filePath), 'utf-8');
    console.log(`✅ ${description}: Found`);
    return content;
  } catch (error) {
    console.log(`❌ ${description}: Missing`);
    return null;
  }
}

function checkContent(content, searchTerm, description) {
  if (content && content.includes(searchTerm)) {
    console.log(`  ✅ ${description}: Found`);
    return true;
  } else {
    console.log(`  ❌ ${description}: Missing`);
    return false;
  }
}

console.log('🔍 Checking Google Analytics 4 & Google Ads Implementation...\n');

// Check core tracking components
console.log('📁 Core Components:');
const gaComponent = checkFile('src/components/GoogleAnalytics.astro', 'Google Analytics component');
const adsComponent = checkFile('src/components/GoogleAds.astro', 'Google Ads component');
const cookieComponent = checkFile('src/components/CookieConsent.astro', 'Cookie Consent component');
const trackingManager = checkFile('src/components/TrackingManager.astro', 'Tracking Manager component');

console.log('\n📄 Configuration Files:');
const envExample = checkFile('.env.example', 'Environment variables example');
const astroConfig = checkFile('astro.config.mjs', 'Astro configuration');

console.log('\n🌐 Translation Files:');
const enTranslations = checkFile('src/i18n/en.json', 'English translations');
const plTranslations = checkFile('src/i18n/pl.json', 'Polish translations');

console.log('\n🔧 Integration Checks:');

// Check Layout integration
const layoutFile = checkFile('src/layouts/Layout.astro', 'Main Layout file');
if (layoutFile) {
  checkContent(layoutFile, 'TrackingManager', 'TrackingManager import');
  checkContent(layoutFile, 'enableTracking', 'Tracking props');
  checkContent(layoutFile, 'pageType', 'Page type support');
}

// Check homepage integration
const homepageFile = checkFile('src/pages/index.astro', 'Homepage file');
if (homepageFile) {
  checkContent(homepageFile, 'data-search-form', 'Search form tracking attribute');
  checkContent(homepageFile, 'trackingManager.trackLocationSearch', 'Search tracking call');
  checkContent(homepageFile, 'pageType="homepage"', 'Homepage page type');
}

// Check language selector integration
const langSelectorFile = checkFile('src/components/LanguageSelector.astro', 'Language Selector file');
if (langSelectorFile) {
  checkContent(langSelectorFile, 'data-language-selector', 'Language selector tracking attribute');
  checkContent(langSelectorFile, 'trackLanguageSwitch', 'Language switch tracking');
}

// Check rest area card integration
const restAreaCardFile = checkFile('src/components/RestAreaCard.astro', 'Rest Area Card file');
if (restAreaCardFile) {
  checkContent(restAreaCardFile, 'data-rest-area-card', 'Rest area card tracking attribute');
  checkContent(restAreaCardFile, 'data-rest-area-id', 'Rest area ID tracking');
}

console.log('\n🔒 Privacy & Compliance:');
if (cookieComponent) {
  checkContent(cookieComponent, 'cookieConsent', 'Cookie consent functionality');
  checkContent(cookieComponent, 'GDPR', 'GDPR compliance mentions');
}

if (enTranslations) {
  checkContent(enTranslations, 'cookieConsent', 'Cookie consent translations (EN)');
}

if (plTranslations) {
  checkContent(plTranslations, 'cookieConsent', 'Cookie consent translations (PL)');
}

console.log('\n📊 Analytics Features:');
if (gaComponent) {
  checkContent(gaComponent, 'gtag', 'Google Analytics gtag');
  checkContent(gaComponent, 'enhanced_ecommerce', 'Enhanced ecommerce');
  checkContent(gaComponent, 'web-vitals', 'Core Web Vitals');
  checkContent(gaComponent, 'consent', 'Consent management');
}

if (adsComponent) {
  checkContent(adsComponent, 'conversion', 'Conversion tracking');
  checkContent(adsComponent, 'remarketing', 'Remarketing');
  checkContent(adsComponent, 'restricted_data_processing', 'Privacy compliance');
}

console.log('\n🌍 Environment Configuration:');
if (envExample) {
  checkContent(envExample, 'PUBLIC_GA4_MEASUREMENT_ID', 'GA4 Measurement ID');
  checkContent(envExample, 'PUBLIC_GOOGLE_ADS_ID', 'Google Ads ID');
  checkContent(envExample, 'PUBLIC_GOOGLE_ADS_CONVERSION_ID', 'Google Ads Conversion ID');
  checkContent(envExample, 'PUBLIC_ENABLE_COOKIE_CONSENT', 'Cookie consent toggle');
}

console.log('\n📋 Summary:');
console.log('To complete the setup:');
console.log('1. Copy .env.example to .env');
console.log('2. Add your Google Analytics 4 Measurement ID');
console.log('3. Add your Google Ads Customer ID and Conversion ID');
console.log('4. Test the implementation in development mode');
console.log('5. Verify tracking in Google Analytics and Google Ads');

console.log('\n🚀 Implementation Complete!');
console.log('The tracking system is ready for your Google Analytics and Google Ads IDs.');
