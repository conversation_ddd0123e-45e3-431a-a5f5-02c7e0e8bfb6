// @ts-check
import { defineConfig } from 'astro/config';
import tailwindcss from "@tailwindcss/vite";
import alpinejs from '@astrojs/alpinejs';
import sitemap from '@astrojs/sitemap';

// https://astro.build/config
export default defineConfig({
  site: 'https://stops24.com',
  i18n: {
    defaultLocale: 'en',
    locales: ['en', 'pl'],
    routing: {
      prefixDefaultLocale: false
    }
  },
  vite: {
    plugins: [tailwindcss()],
    define: {
      // Make environment variables available to client-side code
      'import.meta.env.PUBLIC_GA4_MEASUREMENT_ID': JSON.stringify(process.env.PUBLIC_GA4_MEASUREMENT_ID),
      'import.meta.env.PUBLIC_GOOGLE_ADS_ID': JSON.stringify(process.env.PUBLIC_GOOGLE_ADS_ID),
      'import.meta.env.PUBLIC_GOOGLE_ADS_CONVERSION_ID': JSON.stringify(process.env.PUBLIC_GOOGLE_ADS_CONVERSION_ID),
    }
  },
  integrations: [
    alpinejs(),
    sitemap({
      changefreq: 'weekly',
      priority: 0.7,
      lastmod: new Date(),
      entryLimit: 10000,
    })
  ]
});
