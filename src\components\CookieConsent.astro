---
/**
 * <PERSON><PERSON>sent Banner Component
 * GDPR-compliant cookie consent with granular controls
 */

import { getLangFromUrl, useTranslations } from '../i18n/utils';

export interface Props {
  enableGranularControls?: boolean;
  position?: 'bottom' | 'top' | 'center';
  theme?: 'light' | 'dark' | 'auto';
}

const {
  enableGranularControls = true,
  position = 'bottom',
  theme = 'auto'
} = Astro.props;

const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

// Only show in production or when explicitly enabled
const isProduction = import.meta.env.PROD;
const enableCookieConsent = import.meta.env.PUBLIC_ENABLE_COOKIE_CONSENT !== 'false';
const shouldShowConsent = enableCookieConsent && (isProduction || import.meta.env.PUBLIC_ENABLE_DEV_ANALYTICS === 'true');

// Position classes
const positionClasses = {
  bottom: 'fixed bottom-0 left-0 right-0',
  top: 'fixed top-0 left-0 right-0',
  center: 'fixed inset-0 flex items-center justify-center'
};
---

{shouldShowConsent && (
  <div 
    id="cookie-consent-banner"
    x-data="cookieConsent()"
    x-show="!consentGiven"
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0 transform translate-y-full"
    x-transition:enter-end="opacity-100 transform translate-y-0"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100 transform translate-y-0"
    x-transition:leave-end="opacity-0 transform translate-y-full"
    class={`${positionClasses[position]} z-50 ${position === 'center' ? 'bg-black/50' : ''}`}
    x-cloak
  >
    <div class={`${position === 'center' ? 'max-w-md mx-4' : 'w-full'} bg-white dark:bg-secondary-900 border-t border-secondary-200 dark:border-secondary-700 shadow-lg ${position === 'center' ? 'rounded-lg border' : ''}`}>
      <div class="container-custom py-4">
        <!-- Simple consent view -->
        <div x-show="!showDetails" class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-secondary-900 dark:text-white mb-2">
              {t('cookieConsent.title')}
            </h3>
            <p class="text-sm text-secondary-600 dark:text-secondary-300">
              {t('cookieConsent.description')}
              <a 
                href={currentLang === 'pl' ? '/pl/privacy-policy' : '/privacy-policy'} 
                class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 underline"
                target="_blank"
              >
                {t('cookieConsent.learnMore')}
              </a>
            </p>
          </div>
          
          <div class="flex flex-col sm:flex-row gap-2 min-w-fit">
            {enableGranularControls && (
              <button
                @click="showDetails = true"
                class="px-4 py-2 text-sm font-medium text-secondary-600 hover:text-secondary-700 dark:text-secondary-300 dark:hover:text-secondary-200 border border-secondary-300 dark:border-secondary-600 rounded-md hover:bg-secondary-50 dark:hover:bg-secondary-800 transition-colors"
              >
                {t('cookieConsent.customize')}
              </button>
            )}
            
            <button
              @click="acceptEssential()"
              class="px-4 py-2 text-sm font-medium text-secondary-600 hover:text-secondary-700 dark:text-secondary-300 dark:hover:text-secondary-200 border border-secondary-300 dark:border-secondary-600 rounded-md hover:bg-secondary-50 dark:hover:bg-secondary-800 transition-colors"
            >
              {t('cookieConsent.acceptEssential')}
            </button>
            
            <button
              @click="acceptAll()"
              class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 dark:bg-primary-500 dark:hover:bg-primary-600 rounded-md transition-colors"
            >
              {t('cookieConsent.acceptAll')}
            </button>
          </div>
        </div>

        <!-- Detailed consent view -->
        {enableGranularControls && (
          <div x-show="showDetails" class="space-y-4">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-secondary-900 dark:text-white">
                {t('cookieConsent.customizeTitle')}
              </h3>
              <button
                @click="showDetails = false"
                class="text-secondary-500 hover:text-secondary-700 dark:text-secondary-400 dark:hover:text-secondary-200"
                aria-label="Close details"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <div class="space-y-3">
              <!-- Essential cookies -->
              <div class="flex items-start justify-between p-3 bg-secondary-50 dark:bg-secondary-800 rounded-md">
                <div class="flex-1">
                  <h4 class="font-medium text-secondary-900 dark:text-white">
                    {t('cookieConsent.essential.title')}
                  </h4>
                  <p class="text-sm text-secondary-600 dark:text-secondary-300 mt-1">
                    {t('cookieConsent.essential.description')}
                  </p>
                </div>
                <div class="ml-4">
                  <span class="text-sm text-secondary-500 dark:text-secondary-400">
                    {t('cookieConsent.required')}
                  </span>
                </div>
              </div>

              <!-- Analytics cookies -->
              <div class="flex items-start justify-between p-3 border border-secondary-200 dark:border-secondary-700 rounded-md">
                <div class="flex-1">
                  <h4 class="font-medium text-secondary-900 dark:text-white">
                    {t('cookieConsent.analytics.title')}
                  </h4>
                  <p class="text-sm text-secondary-600 dark:text-secondary-300 mt-1">
                    {t('cookieConsent.analytics.description')}
                  </p>
                </div>
                <div class="ml-4">
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      x-model="preferences.analytics"
                      class="sr-only peer"
                    >
                    <div class="w-11 h-6 bg-secondary-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-secondary-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-secondary-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-secondary-600 peer-checked:bg-primary-600"></div>
                  </label>
                </div>
              </div>

              <!-- Advertising cookies -->
              <div class="flex items-start justify-between p-3 border border-secondary-200 dark:border-secondary-700 rounded-md">
                <div class="flex-1">
                  <h4 class="font-medium text-secondary-900 dark:text-white">
                    {t('cookieConsent.advertising.title')}
                  </h4>
                  <p class="text-sm text-secondary-600 dark:text-secondary-300 mt-1">
                    {t('cookieConsent.advertising.description')}
                  </p>
                </div>
                <div class="ml-4">
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      x-model="preferences.advertising"
                      class="sr-only peer"
                    >
                    <div class="w-11 h-6 bg-secondary-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-secondary-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-secondary-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-secondary-600 peer-checked:bg-primary-600"></div>
                  </label>
                </div>
              </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-2 pt-2">
              <button
                @click="acceptSelected()"
                class="flex-1 px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 dark:bg-primary-500 dark:hover:bg-primary-600 rounded-md transition-colors"
              >
                {t('cookieConsent.savePreferences')}
              </button>
              
              <button
                @click="acceptAll()"
                class="flex-1 px-4 py-2 text-sm font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 border border-primary-600 dark:border-primary-400 rounded-md hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-colors"
              >
                {t('cookieConsent.acceptAll')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  </div>

  <script is:inline>
    function cookieConsent() {
      return {
        consentGiven: false,
        showDetails: false,
        preferences: {
          essential: true, // Always true
          analytics: false,
          advertising: false
        },

        init() {
          // Check if consent was already given
          const savedConsent = localStorage.getItem('cookie-consent');
          if (savedConsent) {
            this.consentGiven = true;
            this.preferences = { ...this.preferences, ...JSON.parse(savedConsent) };
            this.applyConsent();
          }
        },

        acceptAll() {
          this.preferences = {
            essential: true,
            analytics: true,
            advertising: true
          };
          this.saveConsent();
        },

        acceptEssential() {
          this.preferences = {
            essential: true,
            analytics: false,
            advertising: false
          };
          this.saveConsent();
        },

        acceptSelected() {
          this.preferences.essential = true; // Always required
          this.saveConsent();
        },

        saveConsent() {
          localStorage.setItem('cookie-consent', JSON.stringify(this.preferences));
          localStorage.setItem('cookie-consent-date', new Date().toISOString());
          this.consentGiven = true;
          this.applyConsent();
        },

        applyConsent() {
          // Update Google Analytics consent
          if (typeof window.updateGAConsent === 'function') {
            window.updateGAConsent(this.preferences);
          }

          // Update Google Ads consent
          if (typeof window.updateAdsConsent === 'function') {
            window.updateAdsConsent(this.preferences);
          }

          // Update Google AdSense consent
          if (typeof window.adSenseManager !== 'undefined') {
            window.adSenseManager.updateConsent(this.preferences);
          }

          // Dispatch custom event for other scripts
          window.dispatchEvent(new CustomEvent('cookieConsentUpdated', {
            detail: this.preferences
          }));
        },

        // Method to revoke consent (can be called from privacy settings)
        revokeConsent() {
          localStorage.removeItem('cookie-consent');
          localStorage.removeItem('cookie-consent-date');
          this.consentGiven = false;
          this.preferences = {
            essential: true,
            analytics: false,
            advertising: false
          };
          this.applyConsent();
        }
      }
    }

    // Make revoke function globally available
    window.revokeCookieConsent = function() {
      const consentComponent = document.querySelector('[x-data*="cookieConsent"]');
      if (consentComponent && consentComponent._x_dataStack) {
        consentComponent._x_dataStack[0].revokeConsent();
      }
    };

    // Check consent validity (re-ask after 1 year)
    document.addEventListener('DOMContentLoaded', function() {
      const consentDate = localStorage.getItem('cookie-consent-date');
      if (consentDate) {
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
        
        if (new Date(consentDate) < oneYearAgo) {
          // Consent is older than 1 year, ask again
          localStorage.removeItem('cookie-consent');
          localStorage.removeItem('cookie-consent-date');
          location.reload();
        }
      }
    });
  </script>

  <style>
    [x-cloak] { display: none !important; }
  </style>
)}
